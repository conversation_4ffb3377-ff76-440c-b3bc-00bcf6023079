#Requires AutoHotkey >v2.0

; 键盘布局类
class KeyboardLayout {
    static Init() {
        ; 禁用 CapsLock 原始功能
        SetCapsLockState("AlwaysOff")

        Hotkey("CapsLock & Tab", (*) => (
        state := GetKeyState("CapsLock", "T"),
        SetCapsLockState(!state),
        ToolTip(state == 1 ? "🔒" : "🔓"),
        SetTimer(() => ToolTip(), -1000)
        ))
        ; F1-F12 映射
        Hotkey("CapsLock & 1", (*) => Send("{F1}"))
        Hotkey("CapsLock & 2", (*) => Send("{F2}"))
        Hotkey("CapsLock & 3", (*) => Send("{F3}"))
        Hotkey("CapsLock & 4", (*) => Send("{F4}"))
        Hotkey("CapsLock & 5", (*) => Send("{F5}"))
        Hotkey("CapsLock & 6", (*) => Send("{F6}"))
        Hotkey("CapsLock & 7", (*) => Send("{F7}"))
        <PERSON>key("CapsLock & 8", (*) => Send("{F8}"))
        Hotkey("CapsLock & 9", (*) => Send("{F9}"))
        Hotkey("CapsLock & 0", (*) => Send("{F10}"))
        Hotkey("CapsLock & -", (*) => Send("{F11}"))
        Hotkey("CapsLock & =", (*) => Send("{F12}"))

        ; 导航键射（修改）
        Hotkey("CapsLock & i", (*) => Send("{Insert}")) ; CapsLock + I = Insert
        Hotkey("CapsLock & BackSpace", (*) => Send("{Delete}")) ; CapsLock + BackSpace = Delete

        ; 方向键映射（保持不变）

        ; 方向键映射
        Hotkey("CapsLock & w", (*) => Send("{Up down}")) ; 按下时
        Hotkey("CapsLock & w up", (*) => Send("{Up up}")) ; 释放时

        Hotkey("CapsLock & s", (*) => Send("{Down down}"))
        Hotkey("CapsLock & s up", (*) => Send("{Down up}"))

        Hotkey("CapsLock & a", (*) => Send("{Left down}"))
        Hotkey("CapsLock & a up", (*) => Send("{Left up}"))

        Hotkey("CapsLock & d", (*) => Send("{Right down}"))
        Hotkey("CapsLock & d up", (*) => Send("{Right up}"))

        ; 功能键映射
        Hotkey("CapsLock & p", (*) => Send("{PrintScreen}")) ; CapsLock + P = PrintScreen

        ; 多媒体控制
        Hotkey("CapsLock & F8", (*) => Send("{Volume_Mute}")) ; 静音
        Hotkey("CapsLock & F9", (*) => Send("{Volume_Down}")) ; 音量减
        Hotkey("CapsLock & F10", (*) => Send("{Volume_Up}")) ; 音量加
        Hotkey("CapsLock & F11", (*) => Send("{Media_Prev}")) ; 上一曲
        Hotkey("CapsLock & F12", (*) => Send("{Media_Play_Pause}")) ; 播放/暂停
        Hotkey("CapsLock & F1", (*) => Send("{Media_Next}")) ; 下一曲

        ; 特殊字符映射（保持原有并支持组合键）
        Hotkey("+Esc", (*) => Send("~"))
        
        Hotkey("CapsLock & Esc", (*) => Send("``"))
        Hotkey("^+Esc", (*) => Send("^+``"))
        


        ; 代码折叠
        Hotkey("CapsLock & [", (*) => Send("^+[")) ; 折叠
        Hotkey("CapsLock & ]", (*) => Send("^+]")) ; 展开

        ; 特殊功能
        Hotkey("CapsLock & Space", (*) => Send("^{Space}")) ; 触发建议
        Hotkey("CapsLock & .", (*) => Send("^.")) ; 快速复/重构
        Hotkey("CapsLock & p", (*) => Send("^+p")) ; 命令面板
    }
}

; 快速启动类
class QuickLaunch {
    static Init() {
        ; 设置托盘图标
        iconPath := A_ScriptDir "\logo.ico"
        if FileExist(iconPath)
            TraySetIcon(iconPath)

        ; 创建自定义任务栏菜单
        A_TrayMenu.Delete()
        scriptPath := EnvGet("USERPROFILE") "\.config\ahk\keyboard-helper\quick-helper.ahk"
        cmd := 'cmd.exe /k "cursor "' . scriptPath . '"'
        ; 添加自定义菜单项 - 使用引号包裹路径
        A_TrayMenu.Add("编辑脚本", (*) => (
        Run(cmd,,"Hide")
        )) ; 使用引号包裹路径
        A_TrayMenu.Add("重载脚本", (*) => Reload())
        A_TrayMenu.Add() ; 添加分隔线
        A_TrayMenu.Add("启动本地AI", (*) => this.StartLocalAI())
        A_TrayMenu.Add() ; 添加分隔线
        A_TrayMenu.Add("退出", (*) => ExitApp())

        ; 注册快捷键
        Hotkey("!c", (*) => Run("code",,"Hide")) ; Alt + C: 打开 VS Code
        Hotkey("!Enter", (*) => Run("wt")) ; Alt + Enter: 打开 Windows Terminal
        Hotkey("!e", (*) => Run("msedge",,"Hide")) ; Alt + E: 打开 Edge 浏览器
    }

    ; 启动本地AI服务
    static StartLocalAI() {
        try {
            ; 如果已运行则直接结束进程
            if ProcessExist("ollama.exe")
               ProcessClose("ollama.exe")

            ; 等待进程完全结束
            while ProcessExist("ollama.exe")
                Sleep(100)

            ; 使用 cmd 启动服务
            cmd := 'cmd.exe /k "set OLLAMA_ORIGINS=app://obsidian.md* && ollama serve"'
            Run(cmd)

            ; 等待服务启动
            SetTimer(() => (ProcessExist("ollama.exe") 
            ? TrayTip("本地AI服务已启动", "Obsidian Assistant", "Mute") 
            : ""), -3000)
        } catch Error as err {
            MsgBox("启动本地AI服务失败: " err.Message, "错误", "48")
        }
    }
}

; 快速笔记类
class QuickNote {
    static Init() {
        ; 配置 - 使用环境变量设置路径
        this.noteDir := EnvGet("USERPROFILE") "\obsidian-bucket\notes\diary" ; 使用 EnvGet 获取用户目录
        this.templateContent := "
        (
        ---
        tags:
        created_at: {1}
            ---

            ## 💡 想法

            ## 💰 财务

            ## ✅ 任务

            ## 🌈 状态

            )"

            ; 创建 GUI
            this.CreateGui()

            ; 注册快捷键
            Hotkey("#n", (*) => this.ToggleGui()) ; Win + N: 显示/隐藏 GUI
            ; 创建 Ctrl+Enter 热键（但初时禁用）
            Hotkey("^Enter", (*) => this.SaveAndClose(), "Off")
        }

        ; 创建 GUI - 使用响应式布局
        static CreateGui() {
            ; 创建主窗口 - 增加初始尺寸和最小尺寸
            this.gui := Gui("+Resize +MinSize800x600", "快速笔记") ; 增加最小尺寸
            this.gui.Move(,,800, 600) ; 设置初始窗口大小为 800x600
            this.gui.SetFont("s12", "Sarasa Mono SC")
            this.gui.OnEvent("Close", (*) => this.gui.Hide())
            this.gui.OnEvent("Size", (*) => this.ResizeControls())

            ; 顶部工具栏
            this.toolBar := this.gui.Add("GroupBox", "x10 y10 h70", "工具栏")

            ; 设置统一的按钮位置和大小
            buttonY := 35 ; 统一的垂直位置
            buttonH := 25 ; 统一的按钮高度

            ; 日期导航（左侧）
            this.prevButton := this.gui.Add("Button", Format("xp+10 y{1} w30 h{2}", buttonY, buttonH), "<")
            this.prevButton.OnEvent("Click", (*) => this.GotoPrevDay())

            this.dateEdit := this.gui.Add("Edit", Format("x+10 y{1} w120 h{2}", buttonY, buttonH), FormatTime(, "yyyy-MM-dd"))

            this.todayButton := this.gui.Add("Button", Format("x+10 y{1} w50 h{2}", buttonY, buttonH), "今天")
            this.todayButton.OnEvent("Click", (*) => (
            this.dateEdit.Value := FormatTime(, "yyyy-MM-dd"),
            this.LoadNote()
            ))

            ; Obsidian 按钮（右侧）- 使用相同的 y 坐标
            this.obsidianButton := this.gui.Add("Button", Format("x+30 y{1} w70 h{2}", buttonY, buttonH), "打开OB")
            this.obsidianButton.OnEvent("Click", (*) => this.OpenInObsidian())

            ; 编辑区 - 减小底部边距，为更高的状态栏留出空间
            this.editor := this.gui.Add("Edit", "x10 y90 vEditor +Multi +WantTab", "")
            this.editor.SetFont("s14", "LXGW WenKai Mono") ; 改回使用单一字体

            ; 状态栏 - 使用更的字体
            this.statusBar := this.gui.Add("StatusBar",, "就绪")
            this.statusBar.SetFont("s8", "Sarasa Mono SC")
        }

        ; 简化的响应式布局
        static ResizeControls() {
            if !this.gui.HasProp("Hwnd")
                return

            ; 获取窗口客户区尺寸
            clientWidth := 0
            clientHeight := 0
            try {
                this.gui.GetClientPos(,, &clientWidth, &clientHeight)
            }

            ; 计算合适的尺寸
            margin := 10
            toolBarWidth := clientWidth - (margin * 2)
            editorWidth := clientWidth - (margin * 2)
            editorHeight := clientHeight - 110 ; 恢复原来的边距，因为状态栏字体变小了

            ; 更新控件位置和大小
            try {
                ; 调工具栏组宽度
                this.toolBar.Move(margin, margin, toolBarWidth)

                ; 调整编辑器尺寸和位置
                this.editor.Move(margin, 90, editorWidth, editorHeight)

                ; 调整 Obsidian 按钮位置 - 保持垂直位置不变
                buttonX := margin + toolBarWidth - 80 ; 调整按钮宽度为70
                this.obsidianButton.Move(buttonX, 35) ; 使用统一的buttonY值
            }
        }

        ; 显示/隐藏 GUI
        static ToggleGui() {
            if WinExist("ahk_id " this.gui.Hwnd) {
                this.gui.Hide()
            Hotkey("^Enter", "Off") ; 禁用热键
        } else {
            this.LoadNote()
            this.gui.Show()
            Hotkey("^Enter", "On") ; 启用热键
        }
    }

    ; 创建新笔记
    static CreateNote() {
        date := this.dateEdit.Value
        filePath := this.noteDir "\" date ".md"

        if !DirExist(this.noteDir)
            DirCreate(this.noteDir)

        if FileExist(filePath) {
            if (MsgBox("笔记已存在，是否覆盖？", "确认", 4) != "Yes")
                return
        }

        ; 使用当前时间作为 created_at
        now := FormatTime(, "yyyy-MM-dd HH:mm:ss")
        content := Format(this.templateContent, now)
        this.editor.Value := content
        this.SaveNote()
        this.statusBar.Text := "已创建新笔记"
    }

    ; 保存笔记
    static SaveNote() {
        date := this.dateEdit.Value
        filePath := this.noteDir "\" date ".md"

        try {
            FileDelete(filePath)
            FileAppend(this.editor.Value, filePath, "UTF-8")
            this.statusBar.Text := "保存成功"
        } catch Error as e {
            this.statusBar.Text := "保存失败：" e.Message
        }
    }

    ; 加载笔记
    static LoadNote() {
        date := this.dateEdit.Value
        filePath := this.noteDir "\" date ".md"

        ; 如果文件存在，自动创建
        if !FileExist(filePath) {
            if !DirExist(this.noteDir)
                DirCreate(this.noteDir)

            content := Format(this.templateContent, date)
            try {
                FileAppend(content, filePath, "UTF-8")
                this.statusBar.Text := "已自动创建新笔记"
            } catch Error as e {
                this.statusBar.Text := "创建笔记失败：" e.Message
                return
            }
        }

        ; 读取文件内容
        try {
            content := FileRead(filePath, "UTF-8")
            this.editor.Value := content
            this.statusBar.Text := "加载成功"
        } catch Error as e {
            this.statusBar.Text := "加载失败：" e.Message
        }
    }

    ; 在 Obsidian 中打开
    static OpenInObsidian() {
        date := this.dateEdit.Value
        filePath := this.noteDir "\" date ".md"

        ; 保存当前内容
        this.SaveNote()

        ; 构造 Obsidian URI - 使用正确的 vault 名称
        obsidianUri := Format("obsidian://open?vault=notes&file=diary/{1}.md", date)

        ; 使用 Obsidian URI 打开
        try {
            ; 先尝试直接打开 Obsidian
            if !ProcessExist("Obsidian.exe") {
                Run("obsidian://") ; 如果 Obsidian 未运行，先启动它
                Sleep(1000) ; 等待 Obsidian 启动
            }

            ; 然后打开特定笔记
            Run(obsidianUri)
            this.gui.Hide()
        } catch Error as e {
            this.statusBar.Text := "打开 Obsidian 失败：" e.Message
        }
    }

    ; 保存并关闭
    static SaveAndClose() {
        if WinActive("ahk_id " this.gui.Hwnd) { ; 只在笔窗口活动时响应
            this.SaveNote()
        this.gui.Hide()
    }
}

; 转到前一天
static GotoPrevDay() {
    currentDate := this.dateEdit.Value
    currentTime := this.ParseDate(currentDate)
    prevTime := DateAdd(currentTime, -1, "days")
    prevDate := FormatTime(prevTime, "yyyy-MM-dd")

    this.dateEdit.Value := prevDate
    this.LoadNote()
}

; 辅助函数：解析日期字符串为时间戳
static ParseDate(dateStr) {
    ; 假设输入格式为 "yyyy-MM-dd"
    RegExMatch(dateStr, "(\d{4})-(\d{2})-(\d{2})", &match)
    if (match) {
        return match.1 match.2 match.3 "000000" ; 转换为 YYYYMMDDHHMMSS 格式
    }
    return A_Now ; 如解析失败，返回当前时间
}
}

; 初始化键盘布局
KeyboardLayout.Init()

; 初始化快速启动
QuickLaunch.Init()

; 初始化快速笔记
QuickNote.Init() 