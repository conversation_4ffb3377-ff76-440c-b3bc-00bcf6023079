#Requires AutoHotkey >v2.0

; 键盘布局类
class KeyboardLayout {
    static Init() {
        ; 禁用 CapsLock 原始功能
        SetCapsLockState("AlwaysOff")

        Hotkey("CapsLock & Tab", (*) => (
        state := GetKeyState("CapsLock", "T"),
        SetCapsLockState(!state),
        ToolTip(state == 1 ? "🔒" : "🔓"),
        SetTimer(() => ToolTip(), -1000)
        ))
        ; F1-F12 映射
        Hotkey("CapsLock & 1", (*) => Send("{F1}"))
        Hotkey("CapsLock & 2", (*) => Send("{F2}"))
        Hotkey("CapsLock & 3", (*) => Send("{F3}"))
        Hotkey("CapsLock & 4", (*) => Send("{F4}"))
        Hotkey("CapsLock & 5", (*) => Send("{F5}"))
        Hotkey("CapsLock & 6", (*) => Send("{F6}"))
        Hotkey("CapsLock & 7", (*) => Send("{F7}"))
        <PERSON>key("CapsLock & 8", (*) => Send("{F8}"))
        Hotkey("CapsLock & 9", (*) => Send("{F9}"))
        Hotkey("CapsLock & 0", (*) => Send("{F10}"))
        Hotkey("CapsLock & -", (*) => Send("{F11}"))
        Hotkey("CapsLock & =", (*) => Send("{F12}"))

        ; 导航键射（修改）
        Hotkey("CapsLock & i", (*) => Send("{Insert}")) ; CapsLock + I = Insert
        Hotkey("CapsLock & BackSpace", (*) => Send("{Delete}")) ; CapsLock + BackSpace = Delete

        ; 方向键映射（保持不变）

        ; 方向键映射
        Hotkey("CapsLock & w", (*) => Send("{Up down}")) ; 按下时
        Hotkey("CapsLock & w up", (*) => Send("{Up up}")) ; 释放时

        Hotkey("CapsLock & s", (*) => Send("{Down down}"))
        Hotkey("CapsLock & s up", (*) => Send("{Down up}"))

        Hotkey("CapsLock & a", (*) => Send("{Left down}"))
        Hotkey("CapsLock & a up", (*) => Send("{Left up}"))

        Hotkey("CapsLock & d", (*) => Send("{Right down}"))
        Hotkey("CapsLock & d up", (*) => Send("{Right up}"))

        ; 功能键映射
        Hotkey("CapsLock & p", (*) => Send("{PrintScreen}")) ; CapsLock + P = PrintScreen

        ; 多媒体控制
        Hotkey("CapsLock & F8", (*) => Send("{Volume_Mute}")) ; 静音
        Hotkey("CapsLock & F9", (*) => Send("{Volume_Down}")) ; 音量减
        Hotkey("CapsLock & F10", (*) => Send("{Volume_Up}")) ; 音量加
        Hotkey("CapsLock & F11", (*) => Send("{Media_Prev}")) ; 上一曲
        Hotkey("CapsLock & F12", (*) => Send("{Media_Play_Pause}")) ; 播放/暂停
        Hotkey("CapsLock & F1", (*) => Send("{Media_Next}")) ; 下一曲

        ; 特殊字符映射（保持原有并支持组合键）
        Hotkey("+Esc", (*) => Send("~"))
        
        Hotkey("CapsLock & Esc", (*) => Send("``"))
        Hotkey("^+Esc", (*) => Send("^+``"))
        


        ; 代码折叠
        Hotkey("CapsLock & [", (*) => Send("^+[")) ; 折叠
        Hotkey("CapsLock & ]", (*) => Send("^+]")) ; 展开

        ; 特殊功能
        Hotkey("CapsLock & Space", (*) => Send("^{Space}")) ; 触发建议
        Hotkey("CapsLock & .", (*) => Send("^.")) ; 快速复/重构
        Hotkey("CapsLock & p", (*) => Send("^+p")) ; 命令面板
    }
}

; 快速启动类
class QuickLaunch {
    static Init() {
        ; 设置托盘图标
        iconPath := A_ScriptDir "\logo.ico"
        if FileExist(iconPath)
            TraySetIcon(iconPath)

        ; 创建自定义任务栏菜单
        A_TrayMenu.Delete()
        scriptPath := EnvGet("USERPROFILE") "\.config\ahk\keyboard-helper\quick-helper.ahk"
        cmd := 'cmd.exe /k "cursor "' . scriptPath . '"'
        ; 添加自定义菜单项 - 使用引号包裹路径
        A_TrayMenu.Add("编辑脚本", (*) => (
        Run(cmd,,"Hide")
        )) ; 使用引号包裹路径
        A_TrayMenu.Add("重载脚本", (*) => Reload())
        A_TrayMenu.Add() ; 添加分隔
        A_TrayMenu.Add() ; 添加分隔线
        A_TrayMenu.Add("退出", (*) => ExitApp())

        ; 注册快捷键
        Hotkey("!c", (*) => Run("code",,"Hide")) ; Alt + C: 打开 VS Code
        Hotkey("!Enter", (*) => Run("wt")) ; Alt + Enter: 打开 Windows Terminal
        Hotkey("!e", (*) => Run("msedge",,"Hide")) ; Alt + E: 打开 Edge 浏览器
    }

    ; 启动本地AI服务
    static StartLocalAI() {
        try {
            ; 如果已运行则直接结束进程
            if ProcessExist("ollama.exe")
               ProcessClose("ollama.exe")

            ; 等待进程完全结束
            while ProcessExist("ollama.exe")
                Sleep(100)

            ; 使用 cmd 启动服务
            cmd := 'cmd.exe /k "set OLLAMA_ORIGINS=app://obsidian.md* && ollama serve"'
            Run(cmd)

            ; 等待服务启动
            SetTimer(() => (ProcessExist("ollama.exe") 
            ? TrayTip("本地AI服务已启动", "Obsidian Assistant", "Mute") 
            : ""), -3000)
        } catch Error as err {
            MsgBox("启动本地AI服务失败: " err.Message, "错误", "48")
        }
    }
}



; 初始化键盘布局
KeyboardLayout.Init()

; 初始化快速启动
QuickLaunch.Init()

