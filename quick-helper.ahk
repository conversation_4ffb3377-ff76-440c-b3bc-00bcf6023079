#Requires AutoHotkey >v2.0

; 键盘布局类
class KeyboardLayout {
    static Init() {
        ; 禁用 CapsLock 原始功能
        SetCapsLockState("AlwaysOff")

        Hotkey("CapsLock & Tab", (*) => (
        state := GetKeyState("CapsLock", "T"),
        SetCapsLockState(!state),
        ToolTip(state == 1 ? "🔒" : "🔓"),
        SetTimer(() => ToolTip(), -1000)
        ))
        ; F1-F12 映射
        Hotkey("CapsLock & 1", (*) => Send("{F1}"))
        Hotkey("CapsLock & 2", (*) => Send("{F2}"))
        Hotkey("CapsLock & 3", (*) => Send("{F3}"))
        Hotkey("CapsLock & 4", (*) => Send("{F4}"))
        Hotkey("CapsLock & 5", (*) => Send("{F5}"))
        Hotkey("CapsLock & 6", (*) => Send("{F6}"))
        Hotkey("CapsLock & 7", (*) => Send("{F7}"))
        <PERSON>key("CapsLock & 8", (*) => Send("{F8}"))
        Hotkey("CapsLock & 9", (*) => Send("{F9}"))
        Hotkey("CapsLock & 0", (*) => Send("{F10}"))
        Hotkey("CapsLock & -", (*) => Send("{F11}"))
        Hotkey("CapsLock & =", (*) => Send("{F12}"))

        ; 导航键射（修改）
        Hotkey("CapsLock & i", (*) => Send("{Insert}")) ; CapsLock + I = Insert
        Hotkey("CapsLock & BackSpace", (*) => Send("{Delete}")) ; CapsLock + BackSpace = Delete

        ; 方向键映射（保持不变）

        ; 方向键映射
        Hotkey("CapsLock & w", (*) => Send("{Up down}")) ; 按下时
        Hotkey("CapsLock & w up", (*) => Send("{Up up}")) ; 释放时

        Hotkey("CapsLock & s", (*) => Send("{Down down}"))
        Hotkey("CapsLock & s up", (*) => Send("{Down up}"))

        Hotkey("CapsLock & a", (*) => Send("{Left down}"))
        Hotkey("CapsLock & a up", (*) => Send("{Left up}"))

        Hotkey("CapsLock & d", (*) => Send("{Right down}"))
        Hotkey("CapsLock & d up", (*) => Send("{Right up}"))

        ; 功能键映射
        Hotkey("CapsLock & p", (*) => Send("{PrintScreen}")) ; CapsLock + P = PrintScreen

        ; 多媒体控制
        Hotkey("CapsLock & F8", (*) => Send("{Volume_Mute}")) ; 静音
        Hotkey("CapsLock & F9", (*) => Send("{Volume_Down}")) ; 音量减
        Hotkey("CapsLock & F10", (*) => Send("{Volume_Up}")) ; 音量加
        Hotkey("CapsLock & F11", (*) => Send("{Media_Prev}")) ; 上一曲
        Hotkey("CapsLock & F12", (*) => Send("{Media_Play_Pause}")) ; 播放/暂停
        Hotkey("CapsLock & F1", (*) => Send("{Media_Next}")) ; 下一曲

        ; 特殊字符映射（保持原有并支持组合键）
        Hotkey("+Esc", (*) => Send("~"))
        
        Hotkey("CapsLock & Esc", (*) => Send("``"))
        Hotkey("^+Esc", (*) => Send("^+``"))
        


        ; 代码编辑功能（仅在 VS Code 中生效）
        Hotkey("CapsLock & [", (*) => this.SendIfVSCode("^+[")) ; 折叠
        Hotkey("CapsLock & ]", (*) => this.SendIfVSCode("^+]")) ; 展开
        Hotkey("CapsLock & Space", (*) => this.SendIfVSCode("^{Space}")) ; 触发建议
        Hotkey("CapsLock & .", (*) => this.SendIfVSCode("^.")) ; 快速修复/重构
        Hotkey("CapsLock & /", (*) => this.SendIfVSCode("^+p")) ; 命令面板

        ; 快捷键帮助
        Hotkey("CapsLock & h", (*) => HotkeyHelper.ShowHelp()) ; CapsLock + H = 显示快捷键帮助
    }

    ; 检查是否在 VS Code 中，如果是则发送按键
    static SendIfVSCode(keys) {
        ; 检查当前活动窗口是否为 VS Code
        if (WinActive("ahk_exe Code.exe") || WinActive("Visual Studio Code")) {
            Send(keys)
        } else {
            ; 如果不在 VS Code 中，显示提示
            ToolTip("此快捷键仅在 VS Code 中可用")
            SetTimer(() => ToolTip(), -1500)
        }
    }
}

; 快捷键帮助类
class HotkeyHelper {
    static helpGui := ""

    static ShowHelp() {
        ; 如果帮助窗口已存在，则关闭它
        if (this.helpGui && WinExist("ahk_id " this.helpGui.Hwnd)) {
            this.helpGui.Close()
            return
        }

        ; 创建帮助窗口
        this.helpGui := Gui("+Resize +MinSize600x400", "快捷键帮助")
        this.helpGui.SetFont("s10", "Sarasa Mono SC")
        this.helpGui.OnEvent("Close", (*) => this.helpGui.Destroy())
        this.helpGui.OnEvent("Escape", (*) => this.helpGui.Close())

        ; 创建帮助内容
        helpText := "
        (
        ═══════════════════════════════════════════════════════════════
                                    快捷键帮助
        ═══════════════════════════════════════════════════════════════

        【基础功能】
        CapsLock + Tab          切换大写锁定状态
        CapsLock + H            显示/隐藏此帮助窗口

        【F功能键映射】
        CapsLock + 1~0,-,=      F1~F12 功能键

        【方向键映射】
        CapsLock + W            上方向键
        CapsLock + S            下方向键
        CapsLock + A            左方向键
        CapsLock + D            右方向键

        【导航键】
        CapsLock + I            Insert 键
        CapsLock + BackSpace    Delete 键
        CapsLock + P            PrintScreen 截图

        【多媒体控制】
        CapsLock + F8           静音/取消静音
        CapsLock + F9           音量减
        CapsLock + F10          音量加
        CapsLock + F11          上一曲
        CapsLock + F12          播放/暂停
        CapsLock + F1           下一曲

        【特殊字符】
        Shift + Esc             输入 ~ 符号
        CapsLock + Esc          输入 ` 符号
        Ctrl + Shift + Esc      输入 Ctrl+Shift+` 组合

        【代码编辑 - 仅在 VS Code 中可用】
        CapsLock + [            代码折叠
        CapsLock + ]            代码展开
        CapsLock + Space        触发代码建议
        CapsLock + .            快速修复/重构
        CapsLock + /            命令面板

        【应用程序启动】
        Alt + C                 打开 VS Code
        Alt + Enter             打开 Windows Terminal
        Alt + E                 打开 Edge 浏览器

        ═══════════════════════════════════════════════════════════════
        按 Esc 键或点击关闭按钮退出帮助
        )"

        ; 添加文本控件
        this.helpGui.Add("Edit", "x10 y10 w580 h380 ReadOnly -Wrap VScroll", helpText)

        ; 显示窗口
        this.helpGui.Show("w600 h400")
    }
}

; 快速启动类
class QuickLaunch {
    static Init() {
        ; 设置托盘图标
        iconPath := A_ScriptDir "\logo.ico"
        if FileExist(iconPath)
            TraySetIcon(iconPath)

        ; 创建自定义任务栏菜单
        A_TrayMenu.Delete()
        scriptPath := EnvGet("USERPROFILE") "\.config\ahk\keyboard-helper\quick-helper.ahk"
        cmd := 'cmd.exe /k "cursor "' . scriptPath . '"'
        ; 添加自定义菜单项 - 使用引号包裹路径
        A_TrayMenu.Add("快捷键帮助", (*) => HotkeyHelper.ShowHelp())
        A_TrayMenu.Add() ; 添加分隔线
        A_TrayMenu.Add("编辑脚本", (*) => (
        Run(cmd,,"Hide")
        )) ; 使用引号包裹路径
        A_TrayMenu.Add("重载脚本", (*) => Reload())
        A_TrayMenu.Add() ; 添加分隔线
        A_TrayMenu.Add("退出", (*) => ExitApp())

        ; 注册快捷键
        Hotkey("!c", (*) => Run("code",,"Hide")) ; Alt + C: 打开 VS Code
        Hotkey("!Enter", (*) => Run("wt")) ; Alt + Enter: 打开 Windows Terminal
        Hotkey("!e", (*) => Run("msedge",,"Hide")) ; Alt + E: 打开 Edge 浏览器
    }
}



; 初始化键盘布局
KeyboardLayout.Init()

; 初始化快速启动
QuickLaunch.Init()

